# Design Document

## Overview

This design addresses the project lead functionality issues in the Cymatics application. The system currently has a `projectLead` field in the database schema but lacks proper validation and UI components to enforce mandatory selection from predefined leads. The solution involves creating a reusable dropdown component, implementing backend validation, updating the project details display, and ensuring proper integration across create and update workflows.

## Architecture

### Frontend Architecture
- **Component Layer**: Create `ProjectLeadDropdown` component for consistent lead selection
- **Service Layer**: Update `ProjectsService` to handle project lead validation
- **Screen Layer**: Integrate dropdown in create/edit project screens and display in project details
- **Validation Layer**: Client-side validation for mandatory field enforcement

### Backend Architecture
- **Controller Layer**: Add validation middleware for project lead in create/update endpoints
- **Service Layer**: Implement project lead validation logic in `ProjectService`
- **Database Layer**: Utilize existing `projectLead` field with proper constraints

## Components and Interfaces

### Frontend Components

#### ProjectLeadDropdown Component
```typescript
interface ProjectLeadDropdownProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
}

const PROJECT_LEADS = [
  '<PERSON>',
  'Gopinath',
  'Adithyan'
] as const;

type ProjectLead = typeof PROJECT_LEADS[number];
```

#### Updated Project Interfaces
```typescript
// Frontend service interfaces
interface CreateProjectData {
  // ... existing fields
  projectLead: string; // Now mandatory
}

interface UpdateProjectData {
  // ... existing fields
  projectLead?: string; // Optional for updates but validated if provided
}
```

### Backend Interfaces

#### Validation Schema
```typescript
// Validation constants
const VALID_PROJECT_LEADS = ['Vijay Yaso', 'Gopinath', 'Adithyan'] as const;

// Validation functions
interface ProjectLeadValidation {
  validateProjectLead(lead: string): boolean;
  isValidProjectLead(lead: string): lead is typeof VALID_PROJECT_LEADS[number];
}
```

#### Updated Service Interfaces
```typescript
interface CreateProjectData {
  // ... existing fields
  projectLead: string; // Now required
}

interface UpdateProjectData {
  // ... existing fields
  projectLead?: string; // Optional but validated if provided
}
```

## Data Models

### Database Schema
The existing `projectLead` field in the projects table will be utilized:
```sql
-- No schema changes needed, field already exists
-- projects.projectLead: VARCHAR(255) NULL
```

### Validation Rules
- **Create Project**: `projectLead` is required and must be one of the three approved leads
- **Update Project**: `projectLead` is optional but if provided, must be one of the three approved leads
- **Display**: Always show project lead information in project details, with fallback for legacy projects

## Error Handling

### Frontend Error Handling
```typescript
interface ValidationErrors {
  projectLead?: string;
}

// Error messages
const ERROR_MESSAGES = {
  REQUIRED: 'Project lead is required',
  INVALID: 'Please select a valid project lead',
  NETWORK: 'Failed to save project. Please try again.'
};
```

### Backend Error Handling
```typescript
// HTTP Status Codes
// 400 Bad Request: Missing or invalid project lead
// 422 Unprocessable Entity: Validation failed

interface ValidationError {
  field: 'projectLead';
  message: string;
  code: 'REQUIRED' | 'INVALID_VALUE';
}
```

## Testing Strategy

### Frontend Testing
1. **Component Testing**
   - ProjectLeadDropdown renders correctly
   - Dropdown shows exactly three options
   - Selection updates parent component
   - Error states display properly

2. **Integration Testing**
   - Create project form validation
   - Edit project form validation
   - Project details display
   - API integration with validation

3. **User Flow Testing**
   - Complete project creation with lead selection
   - Project update with lead change
   - Error handling for missing lead
   - Project details page display

### Backend Testing
1. **Unit Testing**
   - Project lead validation functions
   - Create project with valid/invalid leads
   - Update project with valid/invalid leads

2. **API Testing**
   - POST /projects with missing projectLead returns 400
   - POST /projects with invalid projectLead returns 400
   - PUT /projects/:id with invalid projectLead returns 400
   - GET /projects/:id includes projectLead in response

3. **Integration Testing**
   - End-to-end project creation workflow
   - End-to-end project update workflow
   - Database constraint validation

## Implementation Approach

### Phase 1: Backend Validation
1. Add project lead validation to `ProjectService`
2. Update create and update methods with validation
3. Add validation middleware to controller endpoints
4. Update error handling and responses

### Phase 2: Frontend Component
1. Create `ProjectLeadDropdown` component
2. Implement dropdown with three predefined options
3. Add validation and error display
4. Create component tests

### Phase 3: Screen Integration
1. Integrate dropdown in `create-project.tsx`
2. Integrate dropdown in `edit-project.tsx`
3. Update project details display in `project-details.tsx`
4. Add client-side validation

### Phase 4: Testing & Validation
1. Test all user flows
2. Verify API validation
3. Test error scenarios
4. Validate project details display

## File Structure

### Frontend Files to Modify/Create
```
cymatics-app/
├── src/components/
│   └── ProjectLeadDropdown.tsx (NEW)
├── app/
│   ├── create-project.tsx (MODIFY)
│   ├── edit-project.tsx (MODIFY)
│   └── project-details.tsx (MODIFY)
└── src/services/
    └── ProjectsService.ts (MODIFY - validation)
```

### Backend Files to Modify
```
cymatics-backend/
├── src/controllers/
│   └── project.controller.ts (MODIFY - validation)
├── src/services/
│   └── project.service.ts (MODIFY - validation)
└── src/utils/
    └── validation.ts (MODIFY - add project lead validation)
```

## Security Considerations

1. **Input Validation**: Server-side validation prevents invalid project leads
2. **Data Integrity**: Consistent validation across create and update operations
3. **XSS Prevention**: Proper sanitization of project lead values
4. **Authorization**: Existing authentication mechanisms apply to project operations

## Performance Considerations

1. **Component Reusability**: Single dropdown component reduces bundle size
2. **Validation Efficiency**: Simple string comparison for lead validation
3. **Database Impact**: No additional queries needed, uses existing field
4. **Caching**: Project lead options can be cached client-side

## Accessibility Considerations

1. **Keyboard Navigation**: Dropdown supports keyboard interaction
2. **Screen Readers**: Proper ARIA labels and descriptions
3. **Error Announcements**: Validation errors announced to screen readers
4. **Focus Management**: Proper focus handling in dropdown component