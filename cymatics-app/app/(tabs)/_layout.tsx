import { Tabs } from 'expo-router';
import React from 'react';
import { Platform } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';

import { HapticTab } from '@/components/HapticTab';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { useTheme } from '@/contexts/ThemeContext';
import { useUser } from '@/contexts/UserContext';

export default function TabLayout() {
  const { colors } = useTheme();
  const { userData, isAuthenticated, isAdmin, isManager } = useUser();

  // Debug logging for role-based tab visibility
  React.useEffect(() => {
    const adminCheck = isAdmin();
    const managerCheck = isManager();
    const showTabs = isAuthenticated && adminCheck;

    console.log('🔍 TabLayout: User role check', {
      userData: userData ? {
        role: userData.role,
        email: userData.email,
        isAuthenticated
      } : 'null',
      isAdmin: adminCheck,
      isManager: managerCheck,
      showAdminTabs: showTabs,
      incomeHref: showTabs ? '/income' : null,
      expenseHref: showTabs ? '/expense' : null
    });
  }, [userData, isAuthenticated, isAdmin, isManager]);

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: colors.tabIconSelected,
        tabBarInactiveTintColor: colors.tabIconDefault,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            backgroundColor: colors.background,
            borderTopColor: colors.border,
          },
          default: {
            backgroundColor: colors.background,
            borderTopColor: colors.border,
          },
        }),
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, focused }) => (
            <MaterialIcons
              name="home"
              size={28}
              color={color || (focused ? colors.tabIconSelected : colors.tabIconDefault)}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="projects"
        options={{
          title: 'Projects',
          tabBarIcon: ({ color, focused }) => (
            <MaterialIcons
              name="description"
              size={28}
              color={color || (focused ? colors.tabIconSelected : colors.tabIconDefault)}
            />
          ),
        }}
      />
      {/* Income tab - only visible to admin users */}
      <Tabs.Screen
        name="income"
        options={{
          title: 'Income',
          href: (isAuthenticated && isAdmin()) ? '/income' : null,
          tabBarIcon: ({ color, focused }) => {
            const iconColor = color || (focused ? colors.tabIconSelected : colors.tabIconDefault);
            console.log('🎨 Income tab icon rendering:', { color, focused, iconColor });
            return (
              <MaterialIcons
                name="account-balance-wallet"
                size={28}
                color={iconColor}
              />
            );
          },
        }}
      />
      {/* Expense tab - only visible to admin users */}
      <Tabs.Screen
        name="expense"
        options={{
          title: 'Expense',
          href: (isAuthenticated && isAdmin()) ? '/expense' : null,
          tabBarIcon: ({ color, focused }) => {
            const iconColor = color || (focused ? colors.tabIconSelected : colors.tabIconDefault);
            console.log('🎨 Expense tab icon rendering:', { color, focused, iconColor });
            return (
              <MaterialIcons
                name="money-off"
                size={28}
                color={iconColor}
              />
            );
          },
        }}
      />
      <Tabs.Screen
        name="calendar"
        options={{
          title: 'Calendar',
          tabBarIcon: ({ color, focused }) => (
            <MaterialIcons
              name="event"
              size={28}
              color={color || (focused ? colors.tabIconSelected : colors.tabIconDefault)}
            />
          ),
        }}
      />
    </Tabs>
  );
}
